# 可视化脚本更新说明

## 概述

本次更新将可视化脚本从使用 `forward_get_pose_raw` 方法改为使用 `forward_get_pose_matched` 方法，以更好地利用模型的匹配功能。

## 主要修改

### 1. 预测函数更新

#### 原函数
```python
def predict_grasps(model: DDPMLightning, batch: Dict, device: str = 'cuda') -> torch.Tensor:
    # 使用 forward_get_pose_raw
    pred_poses = model.forward_get_pose_raw(batch, k=1)
    return pred_poses
```

#### 新函数
```python
def predict_grasps(model: DDPMLightning, batch: Dict, device: str = 'cuda', num_grasps: int = 8) -> torch.Tensor:
    # 使用 forward_get_pose_matched
    matched_preds, matched_targets, outputs, targets = model.forward_get_pose_matched(batch, k=num_grasps)
    
    # 从matched_preds字典中提取手部姿态
    if isinstance(matched_preds, dict):
        if 'hand_model_pose' in matched_preds:
            pred_poses = matched_preds['hand_model_pose']
        elif 'pred_pose_norm' in matched_preds:
            pred_poses = matched_preds['pred_pose_norm']
        else:
            pose_keys = [k for k in matched_preds.keys() if 'pose' in k.lower() or 'hand' in k.lower()]
            if pose_keys:
                pred_poses = matched_preds[pose_keys[0]]
            else:
                raise ValueError(f"无法从matched_preds字典中找到姿态数据")
    else:
        pred_poses = matched_preds
    
    return pred_poses
```

### 2. 新增详细预测函数

```python
def predict_grasps_with_details(model: DDPMLightning, batch: Dict, device: str = 'cuda', num_grasps: int = 8) -> Dict:
    # 返回完整的预测结果，包括outputs和targets
    matched_preds, matched_targets, outputs, targets = model.forward_get_pose_matched(batch, k=num_grasps)
    # ... 处理逻辑
    return {
        'pred_poses': pred_poses,
        'matched_preds': matched_preds,
        'matched_targets': matched_targets,
        'outputs': outputs,
        'targets': targets
    }
```

### 3. 新增mesh创建函数

```python
def create_hand_meshes_from_outputs(outputs: Dict, targets: Dict, batch_size: int, num_grasps: int, max_grasps: int = 3):
    # 直接从模型输出的vertices和faces创建mesh
    # 这比使用HandModel更直接，因为数据已经由模型生成
```

### 4. 输出信息分析

新增了 `print_forward_get_pose_matched_details` 函数来详细分析模型输出：

```python
def print_forward_get_pose_matched_details(outputs: Dict, targets: Dict, batch_size: int, num_grasps: int):
    # 打印outputs['hand']和targets['hand']中的详细信息
    # 包括surface_points, contact_candidates_dis, vertices, faces等
```

## 输出格式说明

### forward_get_pose_matched 返回的数据结构

```python
matched_preds, matched_targets, outputs, targets = model.forward_get_pose_matched(batch, k=num_grasps)
```

其中：
- `matched_preds`: 字典，包含预测的手部姿态数据
  - `hand_model_pose`: 形状 `[B, num_grasps, pose_dim]` - 手部模型姿态
  - `pred_pose_norm`: 形状 `[B, num_grasps, pose_dim]` - 归一化预测姿态
- `matched_targets`: 字典，包含匹配的真实手部姿态数据
  - `hand_model_pose`: 形状 `[B, num_grasps, pose_dim]` - 真实手部模型姿态
  - `norm_pose`: 形状 `[B, num_grasps, pose_dim]` - 归一化真实姿态
- `outputs['hand']`: 包含预测的手部mesh信息
  - `surface_points`: 形状 `[B*num_grasps, 1024, 3]`
  - `contact_candidates_dis`: 形状 `[B*num_grasps, 10000]`
  - `vertices`: 形状 `[B*num_grasps, 29152, 3]`
  - `faces`: 形状 `[12996, 3]`
- `targets['hand']`: 包含真实的手部mesh信息（格式同outputs）

## 使用方法

### 1. 基本使用

```python
# 使用新的预测函数
num_grasps = sample['hand_model_pose'].shape[0]
prediction_result = predict_grasps_with_details(model, batch, device, num_grasps=num_grasps)
pred_poses = prediction_result['pred_poses']
outputs = prediction_result['outputs']
targets = prediction_result['targets']
```

### 2. 可视化使用

```python
# 从模型输出直接创建mesh
pred_meshes, gt_meshes = create_hand_meshes_from_outputs(
    outputs, targets, batch_size, num_grasps, max_grasps=3
)
```

### 3. 测试脚本

运行 `test_forward_get_pose_matched.py` 来验证修改是否正确：

```bash
python test_forward_get_pose_matched.py
```

## 优势

1. **更准确的匹配**: `forward_get_pose_matched` 提供了预测与真实姿态的匹配
2. **直接mesh数据**: 可以从模型输出直接获取手部mesh，无需额外的手部模型
3. **更丰富的信息**: 包含surface_points、contact_candidates_dis等详细信息
4. **更好的可视化**: 预测和真实mesh来自同一数据源，确保一致性

## 兼容性

- 保留了原有的 `create_hand_meshes_comparison` 函数作为回退方案
- 如果模型输出中没有vertices/faces数据，会自动回退到使用HandModel
- 所有原有的可视化功能都保持不变

## 注意事项

1. 确保checkpoint文件与当前模型架构兼容
2. 如果遇到内存问题，可以减少 `num_grasps` 参数
3. 首次运行时会显示详细的输出信息，用于调试和验证

## 错误修复

### 修复 'dict' object has no attribute 'view' 错误

**问题**: 在初始实现中，代码假设 `matched_preds` 是张量，但实际上它是字典。

**解决方案**: 
- 添加了类型检查，正确处理字典格式的 `matched_preds`
- 支持从 `hand_model_pose` 和 `pred_pose_norm` 键中提取姿态数据
- 添加了回退机制，自动查找包含 'pose' 或 'hand' 的键

**修复后的代码**:
```python
if isinstance(matched_preds, dict):
    if 'hand_model_pose' in matched_preds:
        pred_poses = matched_preds['hand_model_pose']
    elif 'pred_pose_norm' in matched_preds:
        pred_poses = matched_preds['pred_pose_norm']
    else:
        pose_keys = [k for k in matched_preds.keys() if 'pose' in k.lower() or 'hand' in k.lower()]
        if pose_keys:
            pred_poses = matched_preds[pose_keys[0]]
        else:
            raise ValueError(f"无法从matched_preds字典中找到姿态数据")
else:
    pred_poses = matched_preds
``` 
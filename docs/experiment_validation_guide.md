# 实验验证指南

## 📋 概述

本指南提供了系统性的实验验证方案，帮助您验证扩散模型的基础功能并优化超参数配置。

## 🎯 验证目标

### 1. 模型基础功能验证
- **目标**：确保模型架构正确，具备基本学习能力
- **验证内容**：数据流、前向传播、反向传播、损失计算

### 2. 超参数优化策略
- **目标**：找到最优的超参数配置
- **优化内容**：学习率、批次大小、扩散步数、损失权重等

## 🚀 验证流程

### 阶段一：快速功能检查

```bash
# 激活环境
source ~/.bashrc && conda activate DexGrasp

# 运行快速验证
python tests/test_quick_validation.py
```

**预期结果**：
- ✅ 模型加载成功
- ✅ 数据加载正常
- ✅ 前向传播工作
- ✅ 损失计算正确
- ✅ 反向传播有效

### 阶段二：综合模型验证

```bash
# 运行综合验证
python tests/test_comprehensive_model_validation.py
```

**验证项目**：
1. **数据流完整性**：验证从原始数据到模型输出的完整流程
2. **前向传播**：检查模型输出格式和维度
3. **梯度流**：分析梯度计算和传播
4. **过拟合能力**：验证模型在小数据集上的学习能力
5. **内存使用**：监控显存和内存占用

**关键指标**：
- 数据维度一致性
- 梯度健康状态（无爆炸/消失）
- 过拟合能力（损失下降>10%）
- 内存使用合理性

### 阶段三：超参数优化

```bash
# 运行超参数优化
python tests/test_hyperparameter_optimization.py
```

**优化参数**：
- `learning_rate`: [1e-4, 5e-4, 1e-3]
- `batch_size`: [4, 8, 16]
- `diffusion_steps`: [50, 100, 200]
- `beta_start/end`: 噪声调度参数
- `guidance_scale`: 无分类器引导强度
- `loss_weights`: 各损失项权重

**评估指标**：
- 最终损失值
- 收敛速度
- 训练稳定性
- 综合得分

## 📊 结果分析

### 1. 模型健康检查

**正常指标**：
```
✅ 梯度范围: [1e-6, 1e-2]
✅ 零梯度参数比例: <30%
✅ 损失下降: >10%
✅ 内存使用: <8GB
```

**异常指标**：
```
❌ 梯度爆炸: >100
❌ 梯度消失: <1e-8
❌ 损失不收敛: 变化<1%
❌ 内存溢出: >12GB
```

### 2. 超参数重要性排序

基于经验，参数重要性通常为：
1. **学习率** (最重要)
2. **损失权重**
3. **扩散步数**
4. **批次大小**
5. **噪声调度**
6. **引导强度**

### 3. 推荐配置

**保守配置**（稳定训练）：
```yaml
learning_rate: 1e-4
batch_size: 8
diffusion_steps: 100
beta_start: 0.0001
beta_end: 0.01
guidance_scale: 7.5
loss_weights:
  translation: 10.0
  rotation: 20.0
  qpos: 1.0
  neg_loss: 0.5
```

**激进配置**（快速收敛）：
```yaml
learning_rate: 5e-4
batch_size: 16
diffusion_steps: 50
beta_start: 0.001
beta_end: 0.02
guidance_scale: 10.0
loss_weights:
  translation: 15.0
  rotation: 25.0
  qpos: 1.5
  neg_loss: 1.0
```

## 🔧 问题诊断

### 常见问题及解决方案

#### 1. 梯度爆炸
**症状**：梯度范数 > 100
**解决**：
- 降低学习率
- 增加梯度裁剪
- 检查损失权重

#### 2. 梯度消失
**症状**：梯度范数 < 1e-8
**解决**：
- 提高学习率
- 检查网络深度
- 调整激活函数

#### 3. 损失不收敛
**症状**：损失变化 < 1%
**解决**：
- 调整学习率
- 检查数据质量
- 修改损失权重

#### 4. 内存溢出
**症状**：CUDA OOM
**解决**：
- 减少批次大小
- 降低点云分辨率
- 使用梯度累积

#### 5. 训练不稳定
**症状**：损失震荡
**解决**：
- 降低学习率
- 增加批次大小
- 使用学习率调度

## 📈 性能基准

### 验证基准

**最低要求**：
- 模型加载成功率: 100%
- 数据流测试通过率: 100%
- 过拟合能力: 损失下降 > 5%

**良好表现**：
- 梯度健康状态: HEALTHY
- 过拟合能力: 损失下降 > 20%
- 训练稳定性: 变异系数 < 0.1

**优秀表现**：
- 收敛速度: 10步内损失下降 > 50%
- 内存效率: <6GB显存
- 参数效率: 有效参数比例 > 90%

## 🎯 实验建议

### 1. 渐进式验证
1. 先运行快速验证确保基本功能
2. 再进行综合验证检查详细指标
3. 最后进行超参数优化

### 2. 分阶段优化
1. **第一阶段**：确保模型能正常训练
2. **第二阶段**：优化收敛速度
3. **第三阶段**：提升最终性能

### 3. 实验记录
- 记录每次实验的配置和结果
- 保存最佳配置用于正式训练
- 建立性能基线用于对比

### 4. 资源管理
- 使用小数据集进行快速迭代
- 在验证阶段限制训练步数
- 及时清理GPU内存

## 📝 总结

通过系统性的验证流程，您可以：
1. 确保模型架构的正确性
2. 识别和解决潜在问题
3. 找到最优的超参数配置
4. 建立性能基准和评估标准

这将为后续的正式训练奠定坚实的基础。

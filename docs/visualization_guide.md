# 预测抓取姿态与真实抓取姿态对比可视化指南

## 概述

本工具提供了一套完整的可视化解决方案，用于比较预训练模型的抓取预测结果与真实标注的抓取姿态。主要功能包括：

- 3D场景中同时显示预测和真实的抓取姿态
- 定量误差分析（位置误差、旋转误差、关节角度误差）
- 批量样本分析和统计
- 灵活的配置文件支持

## 文件结构

```
├── visualize_prediction_vs_ground_truth.py  # 核心可视化脚本
├── visualize_with_config.py                 # 基于配置文件的可视化脚本
├── config_visualization.yaml                # 配置文件模板
└── docs/
    └── visualization_guide.md               # 本使用指南
```

## 快速开始

### 1. 准备工作

确保已安装必要的依赖：
```bash
# 激活conda环境
source ~/.bashrc && conda activate DexGrasp

# 确保已安装项目依赖
pip install open3d pytorch3d omegaconf
```

### 2. 配置模型和数据路径

编辑 `config_visualization.yaml` 文件：

```yaml
# 修改为你的实际路径
model:
  checkpoint_path: "/path/to/your/trained/model.ckpt"
  
dataset:
  root_dir: "/home/<USER>/source/grasp/SceneLeapPro/data/520_0_sub_3"
  succ_grasp_dir: "/home/<USER>/source/grasp/SceneLeapPro/data/succ_collect"
  obj_root_dir: "/home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models"
```

### 3. 运行可视化

#### 方法1：使用配置文件（推荐）

```bash
# 使用默认配置
python visualize_with_config.py

# 使用自定义配置文件
python visualize_with_config.py --config my_config.yaml

# 可视化特定样本
python visualize_with_config.py --sample_idx 5

# 只进行批量分析，不显示可视化窗口
python visualize_with_config.py --batch_only
```

#### 方法2：直接使用核心脚本

```bash
# 需要在脚本中修改路径配置
python visualize_prediction_vs_ground_truth.py
```

## 可视化组件说明

### 颜色编码

- **蓝色系mesh**: 模型预测的抓取姿态
  - 深蓝色：第1个预测抓取
  - 浅蓝色：第2个预测抓取
  - 青色：第3个预测抓取
  
- **红色系mesh**: 真实标注的抓取姿态
  - 红色：第1个真实抓取
  - 橙色：第2个真实抓取
  - 粉红色：第3个真实抓取

- **绿色mesh**: 目标物体的3D模型
- **红色点云**: 目标物体的点云
- **灰色点云**: 背景点云
- **RGB坐标轴**: 世界坐标系参考

### 交互操作

- **鼠标左键拖拽**: 旋转视角
- **鼠标右键拖拽**: 平移视角
- **鼠标滚轮**: 缩放
- **按键操作**: 参考Open3D的标准快捷键

## 误差指标说明

### 位置误差 (Translation Error)
- **计算方法**: 预测位置与真实位置之间的欧几里得距离
- **单位**: 米 (m)
- **含义**: 值越小表示位置预测越准确

### 关节角度误差 (Joint Position Error)
- **计算方法**: 16个关节角度的均方误差 (MSE)
- **单位**: 弧度² (rad²)
- **含义**: 值越小表示手指姿态预测越准确

### 旋转误差 (Rotation Error)
- **计算方法**: 根据旋转表示类型计算
  - R6D: 6D旋转向量的MSE
  - 四元数: 1 - |dot(pred, gt)|
- **含义**: 值越小表示手腕旋转预测越准确

## 配置文件详解

### 数据集配置
```yaml
dataset:
  num_grasps: 8                    # 每个样本的抓取数量
  max_grasps_per_object: 2         # 测试时使用较小值加快速度
  mode: "camera_centric_scene_mean_normalized"  # 坐标系模式
```

### 可视化配置
```yaml
visualization:
  sample_idx: 0                    # 要可视化的样本索引
  max_grasps_to_show: 3           # 最大显示抓取数量
  window_width: 1400              # 窗口宽度
  window_height: 900              # 窗口高度
```

### 批量分析配置
```yaml
batch_analysis:
  enabled: true                   # 是否启用批量分析
  num_samples: 5                  # 分析的样本数量
  save_results: true              # 是否保存结果到文件
```

## 输出结果

### 控制台输出
程序会在控制台输出详细的分析结果：

```
误差统计结果:
位置误差 (米):
  - 平均: 0.0234
  - 标准差: 0.0156
  - 最大: 0.0567
  - 最小: 0.0089

关节角度误差 (MSE):
  - 平均: 0.1234
  - 标准差: 0.0678
  ...
```

### 结果文件
如果启用了结果保存，会生成JSON格式的分析结果文件：

```json
{
  "num_samples": 5,
  "translation_stats": {
    "mean": 0.0234,
    "std": 0.0156,
    "min": 0.0089,
    "max": 0.0567
  },
  "detailed_results": [...]
}
```

## 故障排除

### 常见问题

1. **模型加载失败**
   - 检查checkpoint路径是否正确
   - 确保配置文件路径存在
   - 验证模型架构是否匹配

2. **数据集加载失败**
   - 检查数据路径是否正确
   - 确保数据集格式符合要求
   - 验证文件权限

3. **可视化窗口无法显示**
   - 确保安装了Open3D
   - 检查显示环境（如果是远程服务器）
   - 尝试使用较小的窗口尺寸

4. **内存不足**
   - 减少`max_grasps_per_object`参数
   - 降低`max_points`参数
   - 减少批量分析的样本数量

### 调试技巧

1. **启用详细日志**
   ```yaml
   logging:
     level: "DEBUG"
     show_detailed_errors: true
   ```

2. **使用较小的测试集**
   ```yaml
   dataset:
     max_grasps_per_object: 1
   batch_analysis:
     num_samples: 2
   ```

3. **检查GPU内存使用**
   ```bash
   nvidia-smi
   ```

## 扩展功能

### 自定义颜色方案
可以在配置文件中修改颜色设置：

```yaml
visualization:
  colors:
    prediction:
      - [0.0, 0.0, 1.0]    # 自定义蓝色
    ground_truth:
      - [1.0, 0.0, 0.0]    # 自定义红色
```

### 添加新的误差指标
可以在`calculate_pose_errors`函数中添加自定义的误差计算逻辑。

### 批量处理多个模型
可以修改脚本来比较不同模型的性能。

## 联系支持

如果遇到问题或需要功能扩展，请：
1. 检查本文档的故障排除部分
2. 查看代码中的注释和文档字符串
3. 提交issue或联系开发团队

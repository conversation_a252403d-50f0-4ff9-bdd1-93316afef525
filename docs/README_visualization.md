# 预测抓取姿态与真实抓取姿态对比可视化工具

## 概述

本工具提供了一套完整的可视化解决方案，用于比较预训练模型的抓取预测结果与真实标注的抓取姿态。基于 `visualize_sceneleapplus_dataset.py` 开发，集成了模型推理、姿态匹配、误差分析和3D可视化功能。

## 主要功能

### 🎯 核心功能
- **模型推理**: 加载预训练的DDPMLightning模型进行抓取预测
- **姿态匹配**: 使用模型内部的匹配逻辑将预测抓取与真实抓取进行配对
- **3D可视化**: 在同一场景中同时显示预测和真实的抓取姿态
- **误差分析**: 计算位置误差、旋转误差、关节角度误差等定量指标
- **批量分析**: 支持多样本的统计分析

### 📊 可视化组件
- **蓝色系mesh**: 模型预测的抓取姿态
- **红色系mesh**: 真实标注的抓取姿态  
- **绿色mesh**: 目标物体3D模型
- **红色点云**: 目标物体点云
- **灰色点云**: 背景点云
- **RGB坐标轴**: 世界坐标系参考

### 📈 误差指标
- **位置误差**: 预测位置与真实位置的欧几里得距离 (米)
- **关节角度误差**: 16个关节角度的均方误差 (MSE)
- **旋转误差**: 根据旋转表示类型计算的角度差异

## 文件结构

```
├── visualize_prediction_vs_ground_truth.py  # 核心可视化脚本
├── visualize_with_config.py                 # 配置文件驱动的可视化脚本
├── config_visualization.yaml                # 配置文件模板
├── examples/
│   └── example_visualization.py             # 使用示例
├── tests/
│   └── test_visualization.py                # 功能测试脚本
└── docs/
    └── visualization_guide.md               # 详细使用指南
```

## 快速开始

### 1. 环境准备

```bash
# 激活conda环境
source ~/.bashrc && conda activate DexGrasp

# 确保依赖已安装
pip install open3d pytorch3d omegaconf
```

### 2. 配置设置

编辑 `config_visualization.yaml`:

```yaml
# 修改为你的实际模型路径
model:
  checkpoint_path: "/path/to/your/trained/model.ckpt"

# 数据路径已配置为真实路径
dataset:
  root_dir: "/home/<USER>/source/grasp/SceneLeapPro/data/520_0_sub_3"
  succ_grasp_dir: "/home/<USER>/source/grasp/SceneLeapPro/data/succ_collect"
  obj_root_dir: "/home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models"
```

### 3. 运行可视化

```bash
# 使用配置文件运行（推荐）
python visualize_with_config.py

# 可视化特定样本
python visualize_with_config.py --sample_idx 5

# 只进行批量分析
python visualize_with_config.py --batch_only

# 使用自定义配置
python visualize_with_config.py --config my_config.yaml
```

## 使用示例

### 基础可视化
```python
from visualize_prediction_vs_ground_truth import *

# 创建数据集
dataset = SceneLeapPlusDataset(...)

# 加载模型
model = load_pretrained_model("path/to/checkpoint.ckpt")

# 可视化对比
visualize_prediction_vs_ground_truth(dataset, model, sample_idx=0)
```

### 批量误差分析
```python
# 分析多个样本
stats = batch_analyze_predictions(dataset, model, num_samples=10)
print(f"平均位置误差: {stats['translation_errors_mean']:.4f}")
```

## 测试验证

运行测试脚本验证功能：

```bash
# 运行所有测试
python tests/test_visualization.py

# 查看使用示例
python examples/example_visualization.py
```

测试结果：
```
================================================================================
测试结果总结
================================================================================
数据集加载                ✅ 通过
误差计算                 ✅ 通过
手部模型                 ✅ 通过
点云处理                 ✅ 通过
配置验证                 ✅ 通过
模型加载模拟               ✅ 通过

总计: 6/6 个测试通过
🎉 所有测试通过！可视化功能准备就绪。
```

## 配置说明

### 数据集配置
- `max_grasps_per_object: 2`: 测试时使用较小值加快速度
- `mode`: 支持多种坐标系模式
- `max_points`: 控制点云数量以平衡性能和质量

### 可视化配置
- `max_grasps_to_show: 3`: 控制显示的抓取数量
- `colors`: 自定义预测和真实抓取的颜色方案
- `window_size`: 调整可视化窗口大小

### 批量分析配置
- `num_samples`: 控制分析的样本数量
- `save_results`: 是否保存分析结果到JSON文件

## 输出结果

### 控制台输出
```
误差统计结果:
位置误差 (米):
  - 平均: 0.0234 ± 0.0156
  - 范围: [0.0089, 0.0567]

关节角度误差 (MSE):
  - 平均: 0.1234 ± 0.0678
  ...
```

### 结果文件
生成JSON格式的详细分析结果，包含：
- 聚合统计信息
- 每个样本的详细误差
- 元数据信息

## 技术特性

### 🔧 模型集成
- 支持DDPMLightning模型架构
- 自动推断配置文件路径
- 灵活的checkpoint加载机制

### 🎨 可视化技术
- 基于Open3D的高质量3D渲染
- 支持多种颜色方案
- 交互式视角控制

### 📊 分析功能
- 多维度误差计算
- 统计分析和可视化
- 批量处理能力

### ⚙️ 配置管理
- YAML配置文件支持
- 命令行参数覆盖
- 灵活的参数调整

## 故障排除

### 常见问题
1. **模型加载失败**: 检查checkpoint路径和配置文件
2. **数据集为空**: 验证数据路径和格式
3. **内存不足**: 调整批次大小和点云数量
4. **可视化问题**: 检查显示环境和Open3D安装

### 性能优化
- 使用GPU加速预测
- 调整数据集参数
- 启用点云裁剪

## 扩展功能

### 自定义误差指标
可以在 `calculate_pose_errors` 函数中添加新的误差计算逻辑。

### 多模型比较
可以扩展脚本来比较不同模型的性能。

### 导出功能
支持将可视化结果导出为图片或视频。

## 贡献指南

1. 遵循现有的代码风格
2. 添加适当的文档和注释
3. 编写相应的测试用例
4. 更新使用指南

## 许可证

本工具遵循项目的整体许可证协议。

---

**注意**: 使用前请确保已正确配置数据路径和模型checkpoint路径。详细的使用说明请参考 `docs/visualization_guide.md`。

defaults:
  - _self_
  - model/diffuser@model: diffuser
  # - model/cvae@model: cvae
  # - data: sceneleap
  - data: sceneleapplus

# defaults:
#   - data: sceneleapplus
#   - model: diffuser
#   - distributed: default
#   - wandb: default # Add wandb config group
#   - _self_ 
  # - override hydra/hydra_logging: colorlog
  # - override hydra/job_logging: colorlog
# 基础配置
device: cuda:0
#ncols: 50
epochs: 1000
#print_freq: 500
#validate_freq: 1
save_root: &save_root ./experiments/test_test_test88_1
save_top_n: 10
#log_dir: logs
seed: 42
rot_type: &rot_type r6d
mode: &mode camera_centric_scene_mean_normalized
batch_size: &batch_size 96
use_negative_prompts: &use_negative_prompts false
target_num_grasps: &target_num_grasps 1
fix_num_grasps: &fix_num_grasps True
# 启用固定抓取数量
# fix_num_grasps: true
# target_num_grasps: 50

# # 或者禁用（使用动态数量）
# fix_num_grasps: false
# target_num_grasps: null

# 点云数据格式说明:
# - 输入点云格式: (B, N, 6) = xyz + rgb
# - PointNet2会自动分离xyz坐标和rgb特征
# - 支持文本条件的扩散模型训练

checkpoint_path: ???

# Trainer配置
trainer:
  # 基础训练参数
  max_epochs: ${epochs}
  accelerator: auto  # auto, gpu, cpu
  devices: auto      # auto, 数字, 或列表 [0,1,2,3]
  strategy: auto     # auto, ddp, fsdp, ddp_sharded
  precision: 32      # 16, 32, 64

  # 训练控制
  check_val_every_n_epoch: 1
  gradient_clip_val: 1.0
  accumulate_grad_batches: 1
  benchmark: true
  log_every_n_steps: 20

  # 分布式特定配置
  sync_batchnorm: false
  find_unused_parameters: true

# 分布式训练配置
distributed:
  # 是否启用分布式训练 (auto: 自动检测, true: 强制启用, false: 禁用)
  enabled: auto
  # 分布式策略 (ddp, fsdp, ddp_sharded)
  strategy: ddp
  # 通信后端 (nccl, gloo, mpi)
  backend: nccl
  # GPU设备配置 (auto: 自动检测所有可用GPU, 数字: 指定GPU数量, 列表: 指定具体GPU)
  devices: auto
  # 节点数量 (多机训练)
  num_nodes: 1
  # 是否启用混合精度训练
  precision: 32
  # 梯度累积步数 (用于模拟更大的批次大小)
  accumulate_grad_batches: 1
  # 分布式训练超时时间 (秒)
  timeout: 1800
  # 是否在分布式训练失败时回退到单GPU
  fallback_to_single_gpu: true
  # 是否同步批归一化层
  sync_batchnorm: true
  # 查找未使用参数 (对于复杂模型架构)
  find_unused_parameters: true
  # 学习率缩放方法 (none: 不缩放, linear: 线性缩放, sqrt: 平方根缩放)
  lr_scaling: sqrt

# WandB配置
wandb:
  # 是否启用wandb日志记录
  enabled: True
  # 项目名称
  project: "scene-leap-plus-diffusion-grasp"
  # 实验名称 (如果为null，将自动生成)
  name: null
  # 实验组名 (用于组织相关实验)
  group: null
  # 实验标签
  tags:
    - "diffusion"
    - "grasp-generation"
    - "hand-pose"
    - "scene-leap-plus"
  # 实验备注
  notes: "Scene Leap Plus diffusion model training for grasp generation"
  # 是否保存模型到wandb (建议false以节省流量)
  save_model: false
  # 是否监控系统资源 (可选，会增加少量流量)
  monitor_system: false
  # 日志记录频率 (steps) - 增大以减少流量
  log_freq: 100
  # 是否在分布式训练中只在主进程记录
  log_only_main_process: true

  # 流量优化配置
  optimization:
    # 是否启用可视化 (图片上传消耗较多流量)
    enable_visualization: false
    # 可视化频率 (epoch) - 如果启用，建议设大一些
    visualization_freq: 20
    # 是否记录参数直方图 (消耗较多流量)
    log_histograms: false
    # 直方图记录频率 (epoch)
    histogram_freq: 50
    # 是否记录梯度信息
    log_gradients: false
    # 梯度记录频率 (steps)
    gradient_freq: 1000
    # 系统资源记录频率 (steps)
    system_freq: 500

    monitor_system: false

  # 实验管理配置
  experiment:
    # 是否自动生成实验描述
    auto_description: true
    # 是否记录代码变更 (一次性，流量很小)
    log_code_changes: false
    # 是否记录环境信息 (一次性，流量很小)
    log_environment: true
    # 是否记录数据集信息 (一次性，流量很小)
    log_dataset_info: false

hydra:
  output_subdir: null
  job:
    chdir: False

  hydra_logging:
    version: 1
    disable_existing_loggers: true
  job_logging:
    version: 1
    disable_existing_loggers: true


## 多抓取全局配置
#multi_grasp_config:
#  # 数据配置
#  data:
#    num_grasps_per_scene: 8  # 每个场景的抓取数量
#    grasp_sampling_strategy: "diverse"  # diverse, random, quality_based
#    augmentation:
#      enabled: true
#      rotation_range: 15  # 度
#      translation_range: 0.02  # 米
#      noise_std: 0.001
#
#  # 训练配置
#  training:
#    multi_grasp_enabled: true  # 是否启用多抓取训练
#    backward_compatibility: true  # 是否保持向后兼容
#    progressive_training: false  # 是否使用渐进式训练
#
#  # 推理配置
#  inference:
#    parallel_sampling: true  # 是否使用并行采样
#    output_format: "multi_grasp"  # multi_grasp, best_grasp, top_k
#    post_processing:
#      nms_enabled: true  # 是否启用非极大值抑制
#      nms_threshold: 0.1  # NMS阈值
#      diversity_filtering: true  # 是否进行多样性过滤